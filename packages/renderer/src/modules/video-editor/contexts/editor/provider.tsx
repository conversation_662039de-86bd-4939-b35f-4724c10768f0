import React, { ReactNode, useCallback, useEffect, useState } from 'react'
import { EditorContext } from './context'
import { useOverlays } from './useOverlays'
import { useVideoPlayer } from './useVideoPlayer'
import { useAspectRatio } from './useAspectRatio'
import { useCompositionDuration } from './useCompositionDuration'
import { useHistory } from './useHistory'
import { useProjectSaver } from './useProjectSaver'
import { CloudLoadErrorDialog } from './cloud-load-error-dialog'
import { PageLockedMask } from './page-locked-mask'
import { cleanupPlugins, initializePlugins } from '@/modules/video-editor/resource-plugin-system'
import { cacheManager } from '@/libs/cache/cache-manager'
import { forceCacheResources } from '@/modules/video-editor/utils/track-helper'

export const EditorProvider: React.FC<{
  scriptId: string
  projectId: string
  children: ReactNode
}> = ({ children, scriptId, projectId }) => {
  // 云端加载状态管理
  const [cloudLoadState, setCloudLoadState] = useState<{
    isLoading: boolean
    hasError: boolean
    error?: string
    showErrorDialog: boolean
  }>({
    isLoading: false,
    hasError: false,
    showErrorDialog: false
  })

  const overlaysHook = useOverlays()

  const videoPlayerHook = useVideoPlayer()

  const aspectRatioHook = useAspectRatio()

  const { tracks, setTracksDirectly } = overlaysHook

  const compositionDuration = useCompositionDuration(tracks)

  const historyHook = useHistory(tracks, setTracksDirectly)

  const { saveProject, ProjectSaver } = useProjectSaver(
    scriptId,
    true,
    {
      tracks,
      aspectRatio: aspectRatioHook.aspectRatio,
      playerDimensions: aspectRatioHook.getPlayerDimensions(),
      playerMetadata: {
        width: aspectRatioHook.getPlayerDimensions().playerWidth,
        height: aspectRatioHook.getPlayerDimensions().playerHeight,
        fps: videoPlayerHook.fps,
        durationInFrames: compositionDuration.durationInFrames
      }
    },
    {
      onLoad: async loadedState => {
        if (loadedState) {
          const { tracks, aspectRatio, playerDimensions } = loadedState
          setTracksDirectly(await forceCacheResources(tracks || []))

          if (aspectRatio) aspectRatioHook.setAspectRatio(aspectRatio)
          if (playerDimensions) {
            aspectRatioHook.updatePlayerDimensions(
              playerDimensions.playerWidth,
              playerDimensions.playerHeight
            )
          }
        }
      },
      onCloudLoadStart: useCallback(() => {
        setCloudLoadState(prev => ({ ...prev, isLoading: true, hasError: false }))
      }, []),
      onCloudLoadSuccess: useCallback(() => {
        setCloudLoadState(prev => ({ ...prev, isLoading: false }))
      }, []),
      onCloudLoadError: useCallback((error: Error) => {
        console.error('[EditorProvider] 云端状态加载失败:', error)

        if (error.message === 'CLOUD_LOAD_FAILED') {
          // 云端加载失败，显示错误对话框并锁定编辑器
          setCloudLoadState({
            isLoading: false,
            hasError: true,
            error: '网络连接失败或服务器错误',
            showErrorDialog: true
          })
        } else {
          // 其他错误，静默处理
          setCloudLoadState(prev => ({ ...prev, isLoading: false }))
        }
      }, [])
    }
  )

  useEffect(() => {
    initializePlugins()

    return () => {
      cleanupPlugins()
    }
  }, [])

  // 错误处理函数
  const handleRetryCloudLoad = () => {
    setCloudLoadState(prev => ({ ...prev, showErrorDialog: false }))

    // 重新触发加载
    const loadCloudState = async () => {
      try {
        setCloudLoadState(prev => ({ ...prev, isLoading: true, hasError: false }))

        const cloudState = await cacheManager.projectState.loadProjectState(scriptId, true)

        if (cloudState?.tracks) {
          overlaysHook.updateTracks(cloudState.tracks)
          console.log('[EditorProvider] 云端状态重新加载成功')
        }

        setCloudLoadState(prev => ({ ...prev, isLoading: false }))
      } catch (error) {
        console.error('[EditorProvider] 云端状态重新加载失败:', error)
        setCloudLoadState({
          isLoading: false,
          hasError: true,
          error: '网络连接失败或服务器错误',
          showErrorDialog: true
        })
      }
    }

    void loadCloudState()
  }

  const handleCloseEditor = () => {
    // 关闭编辑器标签页或返回上一页
    window.history.back()
  }

  return (
    <EditorContext.Provider
      value={{
        scriptId,
        projectId,

        ...overlaysHook,
        ...aspectRatioHook,
        ...historyHook,
        ...compositionDuration,
        videoPlayer: videoPlayerHook,
        history: historyHook,

        renderMedia: () => { },
        state: {},
        saveProject,
      }}
    >
      {children}

      <ProjectSaver />

      {/* 云端加载错误对话框 */}
      <CloudLoadErrorDialog
        open={cloudLoadState.showErrorDialog}
        onRetry={handleRetryCloudLoad}
        onClose={handleCloseEditor}
        error={cloudLoadState.error}
      />

      {/* 编辑器锁定遮罩 */}
      <PageLockedMask
        isLoading={cloudLoadState.isLoading}
        hasError={cloudLoadState.hasError && !cloudLoadState.showErrorDialog}
        message={cloudLoadState.error}
      />
    </EditorContext.Provider>
  )
}

