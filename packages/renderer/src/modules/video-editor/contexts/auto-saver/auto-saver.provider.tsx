import React, { ReactNode, useCallback, useEffect, useRef, useState } from 'react'
import { AutoSaverContext } from './auto-saver.context'
import { AUTO_SAVE_INTERVAL } from '@/modules/video-editor/constants'
import { AutosaveStatus } from '@/modules/video-editor/components/autosave/autosave-status'
import { AutosaveRecoveryDialog } from '@/modules/video-editor/components/autosave/autosave-recovery-dialog'
import { CloudLoadErrorDialog } from '../editor/cloud-load-error-dialog'
import { PageLockedMask } from '../editor/page-locked-mask'
import { isEqual } from 'lodash'
import { cacheManager } from '@/libs/cache/cache-manager'
import { EditorState } from '@/libs/cache/parts/editor.cache'

export interface AutoSaverProviderProps {
  children: ReactNode
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 编辑器状态
   */
  editorState: EditorState
  /**
   * 初始加载是否完成
   */
  initialLoadComplete?: boolean
  /**
   * 状态加载回调
   */
  onLoad?: (data: EditorState) => void
  /**
   * 关闭编辑器回调
   */
  onCloseEditor?: () => void
}

/**
 * 自动保存核心逻辑 Hook
 */
function useAutosave(
  projectId: string,
  state: EditorState,
  options: {
    interval?: number
    onLoad?: (data: EditorState) => void
    onSave?: () => void
    onAutosaveDetected?: (timestamp: number) => void
    onCloudLoadStart?: () => void
    onCloudLoadSuccess?: () => void
    onCloudLoadError?: (error: Error) => void
  } = {},
) {
  const {
    interval = AUTO_SAVE_INTERVAL,
    onLoad,
    onSave,
    onAutosaveDetected,
    onCloudLoadStart,
    onCloudLoadSuccess,
    onCloudLoadError
  } = options

  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const lastSavedStateRef = useRef<string>('')
  const autoSaveCheckedRef = useRef(false)

  // 检查现有自动保存，仅在挂载时执行一次
  useEffect(() => {
    const checkForAutosave = async () => {
      if (autoSaveCheckedRef.current) return

      try {
        // 开始云端加载
        if (onCloudLoadStart) onCloudLoadStart()

        const stateFromCloud = await cacheManager.projectState.loadProjectState(projectId, true)

        // 云端加载成功
        onCloudLoadSuccess?.()

        if (stateFromCloud) {
          onLoad?.(stateFromCloud)
        }

        const autoSaved = await cacheManager.projectState.hasAutosave(projectId)

        if (autoSaved && onAutosaveDetected) {
          const sameAsManual = stateFromCloud && isEqual(stateFromCloud.tracks, autoSaved.editorState.tracks)

          if (!sameAsManual) {
            onAutosaveDetected(autoSaved.timestamp)
          }
        }
      } catch (error) {
        console.error('Failed to check for autosave:', error)

        // 云端加载失败
        if (onCloudLoadError) onCloudLoadError(error)
      } finally {
        autoSaveCheckedRef.current = true
      }
    }

    void checkForAutosave()
  }, [projectId, onCloudLoadStart, onCloudLoadSuccess, onCloudLoadError, onAutosaveDetected, onLoad])

  // 设置自动保存定时器
  useEffect(() => {
    // 如果项目ID无效，不启动自动保存
    if (!projectId) return

    const saveIfChanged = async () => {
      const currentStateString = JSON.stringify(state)

      // 只有状态发生变化时才保存
      if (currentStateString !== lastSavedStateRef.current) {
        try {
          await cacheManager.projectState.saveProjectState(projectId, state)
          lastSavedStateRef.current = currentStateString
          if (onSave) onSave()
        } catch (error) {
          console.error('Autosave failed:', error)
        }
      }
    }

    // 设置自动保存间隔
    timerRef.current = setInterval(saveIfChanged, interval)

    // 清理定时器
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
    }
  }, [projectId, state, interval, onSave])

  // 手动保存状态函数
  const saveState = useCallback(async () => {
    try {
      await cacheManager.projectState.saveProjectState(projectId, state, true)
      lastSavedStateRef.current = JSON.stringify(state)
      if (onSave) onSave()
      return true
    } catch (error) {
      console.error('Manual save failed:', error)
      return false
    }
  }, [projectId, state, onSave])

  // 手动加载状态函数
  const loadState = async () => {
    try {
      const loadedState = await cacheManager.projectState.loadProjectState(projectId)
      if (loadedState && onLoad) {
        onLoad(loadedState)
      }
      return loadedState
    } catch (error) {
      console.error('Load failed:', error)
      return null
    }
  }

  return {
    saveState,
    loadState,
  }
}

export const AutoSaverProvider: React.FC<AutoSaverProviderProps> = ({
  children,
  projectId,
  editorState,
  initialLoadComplete = true,
  onLoad,
  onCloudLoadStart,
  onCloudLoadSuccess,
  onCloudLoadError
}) => {
  // 自动保存状态
  const [showRecoveryDialog, setShowRecoveryDialog] = useState(false)
  const [autosaveTimestamp, setAutosaveTimestamp] = useState<number | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaveTime, setLastSaveTime] = useState<number | null>(null)

  // 实现自动保存逻辑
  const { saveState, loadState } = useAutosave(projectId, editorState, {
    interval: AUTO_SAVE_INTERVAL,
    onSave: () => {
      setIsSaving(false)
      setLastSaveTime(Date.now())
    },
    onLoad,
    onAutosaveDetected: timestamp => {
      // 只在初始加载时显示恢复对话框，而不是在活动会话期间
      if (!initialLoadComplete) {
        setAutosaveTimestamp(timestamp)
        setShowRecoveryDialog(true)
      }
    },
    onCloudLoadStart,
    onCloudLoadSuccess,
    onCloudLoadError
  })

  // 处理恢复对话框操作
  const handleRecoverAutosave = async () => {
    const loadedState = await loadState()
    console.debug('loadedState', loadedState)
    setShowRecoveryDialog(false)
  }

  const handleDiscardAutosave = () => {
    setShowRecoveryDialog(false)
  }

  // 手动保存函数，用于键盘快捷键或保存按钮
  const saveProject = useCallback(async () => {
    setIsSaving(true)
    await saveState()
  }, [saveState])

  // 设置键盘快捷键进行手动保存 (Ctrl+S)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault()
        saveProject()
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [saveProject])

  return (
    <AutoSaverContext.Provider
      value={{
        isSaving,
        lastSaveTime,
        saveProject,
      }}
    >
      {children}

      {/* 自动保存状态指示器 */}
      <AutosaveStatus
        isSaving={isSaving}
        lastSaveTime={lastSaveTime}
      />

      {/* 自动保存恢复对话框 */}
      {autosaveTimestamp && (
        <AutosaveRecoveryDialog
          open={showRecoveryDialog}
          projectId={projectId}
          timestamp={autosaveTimestamp}
          onRecover={handleRecoverAutosave}
          onDiscard={handleDiscardAutosave}
          onClose={() => setShowRecoveryDialog(false)}
        />
      )}
    </AutoSaverContext.Provider>
  )
}
